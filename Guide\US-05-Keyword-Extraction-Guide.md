# US-05: Keyword Extraction & NLP Guide 🔍

## 🎯 Overview
US-05 **builds upon US-01 through US-04** by adding intelligent keyword extraction from both resumes and job descriptions. This introduces Natural Language Processing (NLP) capabilities and creates the foundation for matching algorithms in future US implementations.

## 📁 File Structure & Purpose

### **Backend Files**

#### **`app.py` - NLP-Enhanced Application**
```python
# Purpose: Extends US-04 app with keyword extraction capabilities
# Why needed: Processes text content to extract meaningful keywords
# How created: Builds on US-04 with NLP service integration
```

**New Features Added:**
- NLP service initialization (spaCy, NLTK)
- Keyword extraction route registration
- Automatic keyword processing triggers
- Enhanced text analysis capabilities

**Working Flow:**
1. Import US-04 base application (with job descriptions)
2. Initialize NLP libraries (spaCy, NLTK)
3. Register keyword extraction routes
4. Set up automatic keyword processing
5. Maintain all previous functionality

#### **`config.py` - NLP Configuration**
```python
# Purpose: Extends US-04 config with NLP processing settings
# Why needed: Configuration for keyword extraction algorithms
# How created: Adds NLP-specific settings to existing config
```

**New Configuration:**
- `NLTK_DATA_PATH`: NLTK data storage location
- `SPACY_MODEL`: spaCy language model (en_core_web_sm)
- `KEYWORD_EXTRACTION_SETTINGS`: Algorithm parameters
- `TEXT_PROCESSING_LIMITS`: Performance and resource limits

**Integration with Previous US:**
- Maintains all US-01 through US-04 settings
- Adds NLP processing configurations
- Preserves authentication, file upload, and job description settings

#### **`models.py` - Enhanced Models with Keywords**
```python
# Purpose: Extends US-04 models with keyword storage
# Why needed: Store extracted keywords for matching algorithms
# How created: Adds keyword fields to existing Resume and JobDescription models
```

**Enhanced Model Features:**
- **Resume Model**: Adds `technical_keywords`, `soft_skills`, `other_keywords` JSON fields
- **JobDescription Model**: Adds `required_skills`, `preferred_skills`, `keywords` JSON fields
- **New Methods**: `extract_keywords()`, `update_keywords()`, `get_keyword_summary()`
- **Keyword Processing**: Automatic extraction on content save/update

**Database Schema Evolution:**
```sql
-- US-01: users table
-- US-02: users table (unchanged)
-- US-03: users + resumes tables
-- US-04: users + resumes + job_descriptions tables
-- US-05: Enhanced resumes and job_descriptions with keyword fields

ALTER TABLE resumes ADD COLUMN technical_keywords TEXT;
ALTER TABLE resumes ADD COLUMN soft_skills TEXT;
ALTER TABLE resumes ADD COLUMN other_keywords TEXT;

ALTER TABLE job_descriptions ADD COLUMN required_skills TEXT;
ALTER TABLE job_descriptions ADD COLUMN preferred_skills TEXT;
ALTER TABLE job_descriptions ADD COLUMN keywords TEXT;
```

#### **`services/keyword_parser.py` - NLP Service**
```python
# Purpose: Core keyword extraction and NLP processing
# Why needed: Intelligent text analysis and keyword categorization
# How created: New service using spaCy, NLTK, and scikit-learn
```

**Service Capabilities:**
- **Text Preprocessing**: Cleaning, tokenization, normalization
- **Keyword Extraction**: TF-IDF, Named Entity Recognition, POS tagging
- **Skill Categorization**: Technical skills vs. soft skills classification
- **Industry-Specific Processing**: Resume and job description specific algorithms

**Key Methods:**
- `extract_keywords_from_text()`: Main extraction function
- `categorize_skills()`: Classify keywords into categories
- `process_resume_text()`: Resume-specific keyword extraction
- `process_job_description_text()`: Job description-specific extraction
- `clean_and_normalize_text()`: Text preprocessing

**Working Flow:**
1. Receive text content (resume or job description)
2. Clean and preprocess text
3. Apply NLP algorithms (spaCy NER, NLTK tokenization)
4. Extract and categorize keywords
5. Return structured keyword data

#### **`routes/us05_keyword_routes.py` - Keyword Management Routes**
```python
# Purpose: API endpoints for keyword extraction and management
# Why needed: Trigger keyword extraction and retrieve keyword data
# How created: New Blueprint with JWT protection
```

**Endpoints:**
- `POST /api/extract-keywords/<content_type>/<id>`: Extract keywords from resume/job description
- `GET /api/keywords/<content_type>/<id>`: Get extracted keywords
- `PUT /api/keywords/<content_type>/<id>`: Update keywords manually
- `GET /api/keyword-summary`: Get user's keyword analytics

**Working Flow:**
1. Validate JWT token (from US-02)
2. Retrieve content (resume or job description)
3. Process text through keyword_parser.py
4. Store extracted keywords in database
5. Return categorized keyword data

#### **`routes/us05_upload_routes.py` - Enhanced Upload with Auto-Extraction**
```python
# Purpose: Extends US-03/US-04 upload routes with automatic keyword extraction
# Why needed: Seamless keyword extraction on content upload
# How created: Enhances existing upload routes with NLP processing
```

**Enhanced Features:**
- Automatic keyword extraction on resume upload
- Automatic keyword extraction on job description creation
- Background processing for large files
- Keyword extraction status tracking

#### **`requirements.txt` - NLP Dependencies**
```python
# Purpose: Adds NLP packages to US-04 requirements
# Why needed: Natural language processing capabilities
# How created: Extends US-04 requirements with NLP libraries
```

**New Dependencies:**
- `spacy`: Advanced NLP processing
- `nltk`: Natural language toolkit
- `scikit-learn`: Machine learning for text analysis
- `numpy`: Numerical computing for NLP algorithms

### **Frontend Files**

#### **`frontend/us05_keywords.html` - Keyword Analysis Interface**
```html
<!-- Purpose: Display and manage extracted keywords -->
<!-- Why needed: Visual representation of keyword analysis -->
<!-- How created: New page for keyword visualization and management -->
```

**Keyword Display Features:**
- Categorized keyword display (Technical, Soft Skills, Other)
- Keyword frequency and importance scores
- Interactive keyword editing
- Keyword comparison between resumes and job descriptions
- Visual keyword clouds and charts

#### **`frontend/static/js/us05_keywords.js` - Keyword Management Logic**
```javascript
// Purpose: Handles keyword display, editing, and analysis
// Why needed: Client-side keyword management and visualization
// How created: New JavaScript for keyword-specific functionality
```

**Functionality:**
- Trigger keyword extraction via API
- Display categorized keywords with visual indicators
- Edit and update keywords manually
- Compare keywords between resumes and job descriptions
- Real-time keyword analysis and suggestions

**Keyword Extraction Trigger:**
```javascript
// Trigger keyword extraction
async function extractKeywords(contentType, contentId) {
    const response = await fetch(`/api/extract-keywords/${contentType}/${contentId}`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    
    const result = await response.json();
    displayKeywords(result.keywords);
}
```

## 🔄 Complete Working Flow

### **1. Automatic Keyword Extraction Process**
```
1. User uploads resume (US-03) or creates job description (US-04)
2. Content saved to database with extracted text
3. Keyword extraction automatically triggered
4. keyword_parser.py processes text content
5. NLP algorithms extract and categorize keywords
6. Keywords stored in database (JSON format)
7. User can view keywords on keywords page
8. Keywords available for matching algorithms (US-06)
```

### **2. Manual Keyword Management**
```
1. User navigates to keywords page (us05_keywords.html)
2. Views extracted keywords by category
3. Can manually edit or add keywords
4. Changes saved to database
5. Updated keywords used in future matching
```

### **3. NLP Processing Pipeline**
```
Text Input → Preprocessing → Tokenization → POS Tagging → 
NER → Skill Classification → Keyword Categorization → 
Database Storage → API Response
```

## 🏗️ Integration with Previous US

### **Database Schema Evolution**
```sql
-- Previous US: Basic content storage
-- US-05: Enhanced with keyword fields

-- Resume keywords (JSON format)
{
  "technical_keywords": ["Python", "JavaScript", "React", "SQL"],
  "soft_skills": ["Communication", "Leadership", "Problem-solving"],
  "other_keywords": ["Bachelor's", "3 years", "Remote"]
}

-- Job description keywords (JSON format)
{
  "required_skills": ["Python", "Django", "PostgreSQL"],
  "preferred_skills": ["AWS", "Docker", "Kubernetes"],
  "keywords": ["Full-stack", "Agile", "Team player"]
}
```

### **Content Processing Integration**
- **Extends US-03**: Resume text extraction enhanced with keyword analysis
- **Extends US-04**: Job description text enhanced with skill extraction
- **Automatic Processing**: Keywords extracted on content creation/update
- **Backward Compatible**: Existing content can be processed retroactively

### **Service Layer Integration**
- **Builds on file_parser.py**: Adds NLP processing to text extraction
- **New Service Layer**: keyword_parser.py provides NLP capabilities
- **Consistent Patterns**: Same service architecture as file processing

## 🔒 Security & Performance

### **NLP Security**
- Input sanitization before NLP processing
- Resource limits for text processing
- User isolation for keyword data
- Secure storage of extracted keywords

### **Performance Optimization**
- Asynchronous keyword extraction for large files
- Caching of NLP models and processed data
- Batch processing for multiple documents
- Resource monitoring and limits

## 🔗 Sequential Integration Foundation

**US-05 builds on US-01 through US-04:**
- ✅ **User Registration**: From US-01
- ✅ **User Authentication**: From US-02
- ✅ **Resume Upload & Text Extraction**: From US-03
- ✅ **Job Description Management**: From US-04
- ✅ **Keyword Extraction**: New in US-05
- ✅ **NLP Foundation**: Enables intelligent matching

**US-05 enables:**
- **US-06**: Resume-job matching using extracted keywords
- **US-07**: AI suggestions based on keyword analysis
- **US-08**: Dashboard analytics with keyword insights
- **US-09**: Advanced search and filtering by keywords

## 🚀 Next Steps
US-05 provides the NLP foundation for:
- **US-06**: Jaccard similarity matching using extracted keywords
- **US-07**: AI-powered suggestions based on missing keywords
- **US-08**: Keyword-based analytics and insights
- **US-09**: Advanced search and filtering capabilities

**US-05 transforms raw text into structured, analyzable data that powers all intelligent features!** 🔍

## 📊 NLP Libraries & Setup

### **NLTK Setup**
```python
# Download required NLTK data
import nltk
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('wordnet')
nltk.download('averaged_perceptron_tagger')
```

### **spaCy Setup**
```bash
# Install spaCy language model
python -m spacy download en_core_web_sm
```

### **Keyword Categories**
- **Technical Skills**: Programming languages, frameworks, tools
- **Soft Skills**: Communication, leadership, teamwork
- **Certifications**: Professional certifications and degrees
- **Experience**: Years of experience, industry terms
- **Other**: Location, company size, work arrangements
