# US-02: Login & JWT Authentication Guide 🔐

## 🎯 Overview
US-02 **builds upon US-01** by adding login functionality and JWT (JSON Web Token) authentication. This establishes the security foundation for all protected features in future US implementations.

## 📁 File Structure & Purpose

### **Backend Files**

#### **`app.py` - Enhanced Flask Application**
```python
# Purpose: Extends US-01 app with JWT authentication
# Why needed: Adds JWT manager and login capabilities
# How created: Builds on US-01 foundation with JWT-Extended
```

**New Features Added:**
- JWT Manager initialization
- Token configuration (access/refresh tokens)
- Protected route decorators
- Token blacklist handling
- Enhanced error handling for authentication

**Working Flow:**
1. Import US-01 base application
2. Add JWT-Extended configuration
3. Initialize JWT manager with app
4. Configure token expiration times
5. Add token validation middleware

#### **`config.py` - JWT Configuration Added**
```python
# Purpose: Extends US-01 config with JWT settings
# Why needed: Secure token generation and validation
# How created: Adds JWT-specific configuration to existing config
```

**New Configuration:**
- `JWT_SECRET_KEY`: Token signing key
- `JWT_ACCESS_TOKEN_EXPIRES`: Token lifetime (24 hours)
- `JWT_REFRESH_TOKEN_EXPIRES`: Refresh token lifetime (30 days)
- `JWT_ALGORITHM`: Signing algorithm (HS256)

**Integration with US-01:**
- Keeps all US-01 database settings
- Adds JWT settings on top
- Maintains backward compatibility

#### **`models.py` - Enhanced User Model**
```python
# Purpose: Extends US-01 User model with JWT methods
# Why needed: Token generation and user authentication
# How created: Adds methods to existing User class
```

**New Methods Added:**
- `generate_tokens()`: Creates access and refresh tokens
- `update_last_login()`: Tracks login timestamps
- `to_dict()`: Serializes user data for responses

**Enhanced Features:**
- Last login tracking
- Token payload customization
- User session management

**Working Flow:**
1. Inherit all US-01 User model features
2. Add JWT token generation methods
3. Implement login timestamp tracking
4. Create user serialization for API responses

#### **`routes/us02_auth_routes.py` - Login Routes**
```python
# Purpose: Adds login endpoint to US-01 registration
# Why needed: User authentication and token issuance
# How created: New Blueprint extending US-01 auth routes
```

**New Endpoints:**
- `POST /api/login`: User authentication with JWT tokens
- `POST /api/refresh`: Refresh token endpoint
- `POST /api/logout`: Token invalidation

**Working Flow:**
1. Receive login credentials (email/password)
2. Validate user exists and password correct
3. Generate JWT access and refresh tokens
4. Update last login timestamp
5. Return tokens and user data

#### **`requirements.txt` - JWT Dependencies**
```python
# Purpose: Adds JWT packages to US-01 requirements
# Why needed: JWT token handling capabilities
# How created: Extends US-01 requirements with new packages
```

**New Dependencies:**
- `Flask-JWT-Extended`: JWT token management
- `PyJWT`: JWT token creation/validation

### **Frontend Files**

#### **`frontend/us02_login.html` - Login Form**
```html
<!-- Purpose: User login interface -->
<!-- Why needed: Authentication entry point for users -->
<!-- How created: HTML form similar to US-01 registration -->
```

**Form Features:**
- Email input (required)
- Password input (required)
- Remember me option
- Forgot password link (placeholder)
- Link to registration page

#### **`frontend/static/js/us02_login.js` - Login Logic**
```javascript
// Purpose: Handles login form submission and token storage
// Why needed: Client-side authentication management
// How created: Extends US-01 JavaScript patterns
```

**Functionality:**
- Form validation before submission
- AJAX login request to backend
- JWT token storage in localStorage
- Automatic redirection after login
- Token expiration handling

**Token Management:**
```javascript
// Store tokens after successful login
localStorage.setItem('dr_resume_token', result.tokens.access_token);
localStorage.setItem('dr_resume_refresh_token', result.tokens.refresh_token);
localStorage.setItem('dr_resume_user', JSON.stringify(result.user));
```

## 🔄 Complete Working Flow

### **1. User Login Process**
```
1. User visits login page (us02_login.html)
2. Enters email and password
3. JavaScript validates input (us02_login.js)
4. AJAX POST to /api/login (us02_auth_routes.py)
5. Backend validates credentials (models.py)
6. JWT tokens generated and returned
7. Tokens stored in browser localStorage
8. User redirected to dashboard/protected area
```

### **2. JWT Token Flow**
```
1. User logs in successfully
2. Server generates access token (24h) and refresh token (30d)
3. Tokens sent to frontend
4. Frontend stores tokens in localStorage
5. All future API requests include access token in headers
6. Server validates token on protected routes
7. Token refreshed automatically when expired
```

### **3. Protected Route Access**
```
1. Frontend makes API request with Authorization header
2. Backend JWT middleware validates token
3. If valid: request proceeds to route handler
4. If invalid/expired: 401 Unauthorized response
5. Frontend handles 401 by refreshing token or redirecting to login
```

## 🏗️ Integration with US-01

### **Database Integration**
- **Reuses US-01 User table**: No schema changes needed
- **Extends User model**: Adds methods without breaking existing functionality
- **Maintains compatibility**: US-01 registration still works

### **Configuration Integration**
- **Extends US-01 config**: Adds JWT settings to existing configuration
- **Preserves database settings**: Same SQLite connection
- **Backward compatible**: US-01 features remain functional

### **Route Integration**
- **Separate Blueprint**: us02_auth_routes.py doesn't interfere with US-01
- **Shared patterns**: Uses same validation and response patterns
- **Modular design**: Can be imported independently

## 🔒 Security Implementation

### **Password Security**
- Inherits US-01 password hashing
- Validates against hashed passwords
- No plain text password storage

### **JWT Security**
- Signed tokens prevent tampering
- Configurable expiration times
- Refresh token rotation
- Secret key protection

### **API Security**
- Protected routes require valid tokens
- Token validation on every request
- Automatic token expiration handling
- CORS configuration for secure frontend communication

## 🔗 Sequential Integration Foundation

**US-02 builds on US-01:**
- ✅ **User Registration**: From US-01 (unchanged)
- ✅ **User Login**: New in US-02
- ✅ **JWT Authentication**: Foundation for all future protected features
- ✅ **Token Management**: Client-side token handling
- ✅ **Protected Routes**: Pattern for US-03+ features

**US-02 enables:**
- **US-03**: Protected resume upload
- **US-04**: Protected job description management
- **US-05**: Protected keyword extraction
- **US-06+**: All protected features require US-02 authentication

## 🚀 Next Steps
US-02 provides the authentication foundation for:
- **US-03**: Resume upload (requires login)
- **US-04**: Job descriptions (user-specific data)
- **US-05**: Keywords (user-specific processing)
- **All future US**: Protected by JWT authentication

**US-02 makes secure, user-specific features possible in all subsequent US!** 🔐
