# US-03: Resume Upload & File Parsing Guide 📤

## 🎯 Overview
US-03 **builds upon US-01 & US-02** by adding resume upload functionality with file parsing capabilities. This introduces file handling, text extraction, and extends the database schema with Resume model.

## 📁 File Structure & Purpose

### **Backend Files**

#### **`app.py` - File Upload Application**
```python
# Purpose: Extends US-02 app with file upload capabilities
# Why needed: Handles multipart form data and file storage
# How created: Builds on US-02 with file upload configuration
```

**New Features Added:**
- File upload configuration (max size, allowed extensions)
- Upload folder creation and management
- Multipart form data handling
- File validation middleware
- Resume management endpoints

**Working Flow:**
1. Import US-02 base application (with JWT)
2. Add file upload configuration
3. Create upload directories if they don't exist
4. Register resume upload routes
5. Configure file size and type restrictions

#### **`config.py` - File Upload Configuration**
```python
# Purpose: Extends US-02 config with file handling settings
# Why needed: Secure file upload and storage management
# How created: Adds file-specific settings to existing config
```

**New Configuration:**
- `UPLOAD_FOLDER`: File storage location (shared/uploads)
- `RESUME_UPLOAD_FOLDER`: Resume-specific subfolder
- `MAX_CONTENT_LENGTH`: File size limit (16MB)
- `ALLOWED_RESUME_EXTENSIONS`: Permitted file types (pdf, doc, docx)

**Integration with US-01/US-02:**
- Maintains all previous database and JWT settings
- Adds file handling on top of existing configuration
- Points to shared storage location

#### **`models.py` - Resume Model Added**
```python
# Purpose: Extends US-02 models with Resume table
# Why needed: Store resume metadata and extracted text
# How created: New Resume model with relationship to User
```

**Resume Model Features:**
- **Fields**: id, user_id, original_filename, stored_filename, file_path, file_size, extracted_text, created_at
- **Relationships**: Foreign key to User (one-to-many)
- **Methods**: `to_dict()`, `get_file_extension()`, `delete_file()`
- **Validation**: File type and size validation

**Database Schema Evolution:**
```sql
-- US-01: users table
-- US-02: users table (unchanged)
-- US-03: users + resumes tables (with foreign key relationship)
```

**Working Flow:**
1. Inherit all US-02 User model features
2. Add Resume model with user relationship
3. Implement file metadata storage
4. Add text extraction storage
5. Create file management methods

#### **`routes/us03_upload_routes.py` - Upload Routes**
```python
# Purpose: Handles resume upload and management
# Why needed: File upload, parsing, and CRUD operations
# How created: New Blueprint with JWT protection
```

**Endpoints:**
- `POST /api/upload-resume`: Upload and parse resume file
- `GET /api/resumes`: List user's resumes
- `GET /api/resume/<id>`: Get specific resume details
- `DELETE /api/resume/<id>`: Delete resume and file

**Working Flow:**
1. Validate JWT token (from US-02)
2. Check file type and size
3. Generate unique filename (UUID)
4. Save file to shared/uploads/resumes/
5. Extract text content using file parser
6. Store metadata and text in database
7. Return success response with resume data

#### **`services/file_parser.py` - Text Extraction Service**
```python
# Purpose: Extract text from PDF, DOC, DOCX files
# Why needed: Convert resume files to searchable text
# How created: Service layer with multiple parsing libraries
```

**Parsing Capabilities:**
- **PDF**: PyPDF2 library for text extraction
- **DOCX**: python-docx library for Word documents
- **DOC**: Basic text extraction (limited support)

**Service Methods:**
- `extract_text_from_file()`: Main extraction function
- `extract_pdf_text()`: PDF-specific extraction
- `extract_docx_text()`: DOCX-specific extraction
- `clean_extracted_text()`: Text cleaning and formatting

**Working Flow:**
1. Receive file path and type
2. Determine appropriate parser based on extension
3. Extract raw text content
4. Clean and format extracted text
5. Return processed text for database storage

#### **`requirements.txt` - File Processing Dependencies**
```python
# Purpose: Adds file parsing packages to US-02 requirements
# Why needed: PDF and Word document processing
# How created: Extends US-02 requirements with parsing libraries
```

**New Dependencies:**
- `PyPDF2`: PDF text extraction
- `python-docx`: Word document processing
- `Werkzeug`: File handling utilities (already included)

### **Frontend Files**

#### **`frontend/us03_upload.html` - Upload Interface**
```html
<!-- Purpose: Resume upload form with drag-and-drop -->
<!-- Why needed: User-friendly file upload experience -->
<!-- How created: HTML5 file input with drag-drop JavaScript -->
```

**Upload Features:**
- Drag-and-drop file area
- File type validation (client-side)
- Upload progress indication
- File preview before upload
- Multiple file format support

#### **`frontend/us03_dashboard.html` - Resume Management**
```html
<!-- Purpose: Display and manage uploaded resumes -->
<!-- Why needed: View, download, and delete resume files -->
<!-- How created: Extends basic dashboard with resume list -->
```

**Dashboard Features:**
- List of uploaded resumes
- File details (name, size, upload date)
- Download and delete actions
- Upload new resume button
- Extracted text preview

#### **`frontend/static/js/us03_upload.js` - Upload Logic**
```javascript
// Purpose: Handles file upload with progress and validation
// Why needed: Client-side file handling and API communication
// How created: Extends US-02 patterns with file upload
```

**Functionality:**
- Drag-and-drop event handling
- File type and size validation
- FormData creation for multipart upload
- Upload progress tracking
- JWT token inclusion in requests
- Error handling and user feedback

**File Upload Flow:**
```javascript
// Create FormData with file and JWT token
const formData = new FormData();
formData.append('resume', file);

// Upload with progress tracking
fetch('/api/upload-resume', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`
    },
    body: formData
});
```

## 🔄 Complete Working Flow

### **1. Resume Upload Process**
```
1. User logs in (US-02 authentication)
2. Navigates to upload page (us03_upload.html)
3. Selects or drags file to upload area
4. JavaScript validates file type/size (us03_upload.js)
5. FormData sent to /api/upload-resume (us03_upload_routes.py)
6. Backend validates JWT token and file
7. File saved to shared/uploads/resumes/ with UUID name
8. Text extracted using file_parser.py service
9. Resume metadata and text stored in database
10. Success response with resume details
```

### **2. File Storage Architecture**
```
shared/
└── uploads/
    └── resumes/
        ├── a1b2c3d4-e5f6-7890-abcd-ef1234567890.pdf
        ├── b2c3d4e5-f6g7-8901-bcde-f23456789012.docx
        └── ...
```

### **3. Text Extraction Process**
```
1. File uploaded and saved to disk
2. file_parser.py determines file type
3. Appropriate parser (PyPDF2/python-docx) extracts text
4. Raw text cleaned and formatted
5. Extracted text stored in database
6. Original file kept for download/re-processing
```

## 🏗️ Integration with US-01 & US-02

### **Database Schema Evolution**
```sql
-- US-01: users table
CREATE TABLE users (
    id, first_name, last_name, email, password_hash, created_at, updated_at
);

-- US-03: adds resumes table
CREATE TABLE resumes (
    id, user_id, original_filename, stored_filename, 
    file_path, file_size, extracted_text, created_at
);
```

### **Authentication Integration**
- **Requires US-02 JWT**: All upload endpoints protected
- **User-specific data**: Resumes linked to authenticated user
- **Token validation**: Every request validates JWT token

### **Configuration Integration**
- **Extends US-02 config**: Adds file settings to existing config
- **Shared storage**: Uses shared/uploads/ for all US implementations
- **Backward compatible**: US-01/US-02 features unchanged

## 🔒 Security Implementation

### **File Security**
- File type validation (whitelist approach)
- File size limits prevent DoS attacks
- UUID filenames prevent path traversal
- User isolation (can only access own files)

### **Authentication Security**
- All endpoints require valid JWT tokens
- User can only upload/access their own resumes
- File operations tied to authenticated user ID

### **Storage Security**
- Files stored outside web root
- Unique filenames prevent conflicts
- Metadata stored separately from files

## 🔗 Sequential Integration Foundation

**US-03 builds on US-01 & US-02:**
- ✅ **User Registration**: From US-01
- ✅ **User Authentication**: From US-02
- ✅ **File Upload**: New in US-03
- ✅ **Text Extraction**: Foundation for keyword parsing
- ✅ **Resume Storage**: User-specific file management

**US-03 enables:**
- **US-04**: Job descriptions (similar file handling patterns)
- **US-05**: Keyword extraction (uses extracted text from US-03)
- **US-06**: Resume matching (compares resume text with job descriptions)
- **US-07+**: All features that analyze resume content

## 🚀 Next Steps
US-03 provides the file handling foundation for:
- **US-04**: Job description upload and storage
- **US-05**: Keyword extraction from resume text
- **US-06**: Resume-job matching algorithms
- **US-07**: AI-powered suggestions based on resume content

**US-03 makes content-based features possible by providing text extraction and file management!** 📤
