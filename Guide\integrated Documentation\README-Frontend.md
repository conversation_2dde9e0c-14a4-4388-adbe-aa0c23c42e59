# 🎨 Frontend Deep Dive - Dr. Resume Integration

## 📋 **Table of Contents**
1. [Frontend Architecture](#frontend-architecture)
2. [HTML Templates](#html-templates)
3. [CSS Styling System](#css-styling-system)
4. [JavaScript Architecture](#javascript-architecture)
5. [User Interface Flow](#user-interface-flow)
6. [Integration Patterns](#integration-patterns)
7. [Responsive Design](#responsive-design)

---

## 🏗️ **Frontend Architecture**

The Dr. Resume frontend evolved through US1-US10 into a cohesive, modern web application:

```
Frontend Structure
├── 📄 HTML Templates (10 pages)
│   ├── Landing & Auth (US1-US2)
│   ├── Document Management (US3-US4)
│   ├── Analysis & Matching (US5-US6)
│   ├── AI Suggestions (US7)
│   └── Dashboard & Account (US8-US10)
├── 🎨 CSS Styling
│   ├── us10_styles.css (Unified design system)
│   └── Component-specific styles
└── ⚡ JavaScript Modules
    ├── Page-specific controllers
    ├── API communication layer
    └── UI interaction handlers
```

### **Design Principles**
- **Mobile-First**: Responsive design for all devices
- **Progressive Enhancement**: Works without JavaScript, enhanced with it
- **Accessibility**: WCAG 2.1 compliant
- **Performance**: Optimized loading and interactions
- **Consistency**: Unified design language across all pages

---

## 📄 **HTML Templates**

### **Template Structure**
All templates follow a consistent structure:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Title - Dr. Resume</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/us10_styles.css') }}">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <!-- Navigation content -->
    </nav>
    
    <!-- Main Content -->
    <main class="container-fluid">
        <!-- Page-specific content -->
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <!-- Footer content -->
    </footer>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/page-specific.js') }}"></script>
</body>
</html>
```

### **Key Templates**

#### **1. Landing Page** (`us10_landing.html`)
**Purpose**: Welcome users and showcase features
```html
<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    Optimize Your Resume with AI
                </h1>
                <p class="lead mb-4">
                    Get personalized suggestions to improve your resume 
                    and increase your chances of landing your dream job.
                </p>
                <div class="d-flex gap-3">
                    <a href="/register" class="btn btn-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Get Started
                    </a>
                    <a href="/login" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="/static/images/hero-illustration.svg" 
                     alt="Resume optimization" class="img-fluid">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="feature-card text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-upload fa-3x text-primary"></i>
                    </div>
                    <h4>Upload Resume</h4>
                    <p>Upload your resume in PDF or DOCX format for analysis</p>
                </div>
            </div>
            <!-- More feature cards... -->
        </div>
    </div>
</section>
```

#### **2. Dashboard** (`us10_dashboard.html`)
**Purpose**: Central hub for all user activities
```html
<!-- Dashboard Header -->
<div class="dashboard-header bg-light py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-1">Welcome back, <span id="userName">User</span>!</h1>
                <p class="text-muted mb-0">Manage your resumes and job applications</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-primary" onclick="location.href='/upload'">
                    <i class="fas fa-plus me-2"></i>Upload Resume
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="container my-4">
    <div class="row">
        <div class="col-md-3 mb-3">
            <div class="stat-card bg-primary text-white p-4 rounded">
                <div class="d-flex align-items-center">
                    <div class="stat-icon me-3">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                    <div>
                        <h4 class="mb-0" id="resumeCount">0</h4>
                        <small>Resumes</small>
                    </div>
                </div>
            </div>
        </div>
        <!-- More stat cards... -->
    </div>
</div>

<!-- Recent Activity -->
<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div id="recentActivity">
                        <!-- Dynamically loaded content -->
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/upload" class="btn btn-outline-primary">
                            <i class="fas fa-upload me-2"></i>Upload Resume
                        </a>
                        <a href="/add_jd" class="btn btn-outline-success">
                            <i class="fas fa-briefcase me-2"></i>Add Job Description
                        </a>
                        <a href="/suggestions" class="btn btn-outline-info">
                            <i class="fas fa-lightbulb me-2"></i>Get Suggestions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### **3. Suggestions Page** (`us10_suggestions.html`)
**Purpose**: AI-powered resume optimization
```html
<!-- Suggestions Header -->
<div class="suggestions-header bg-gradient-info text-white py-4">
    <div class="container">
        <h1 class="h3 mb-1">
            <i class="fas fa-magic me-2"></i>AI Resume Suggestions
        </h1>
        <p class="mb-0">Get personalized suggestions to improve your resume</p>
    </div>
</div>

<!-- Selection Form -->
<div class="container my-4">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group mb-3">
                <label for="resumeSelect" class="form-label">Select Resume:</label>
                <select id="resumeSelect" class="form-select">
                    <option value="">Select a resume...</option>
                    <!-- Dynamically populated -->
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group mb-3">
                <label for="jobSelect" class="form-label">Select Job Description:</label>
                <select id="jobSelect" class="form-select">
                    <option value="">Select a job description...</option>
                    <!-- Dynamically populated -->
                </select>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-md-6">
            <button id="generateBasicBtn" class="btn btn-primary btn-lg w-100">
                <i class="fas fa-check me-2"></i>Generate Basic Suggestions
            </button>
        </div>
        <div class="col-md-6">
            <button id="generatePremiumBtn" class="btn btn-warning btn-lg w-100">
                <i class="fas fa-crown me-2"></i>Generate Premium Suggestions
            </button>
        </div>
    </div>
</div>

<!-- Resume-Job Match Analysis -->
<div class="container mb-4">
    <div class="card">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>Resume-Job Match Analysis
            </h5>
        </div>
        <div class="card-body" id="matchingScoreContent">
            <!-- Dynamically populated with match scores -->
        </div>
    </div>
</div>

<!-- Suggestions Results -->
<div class="container">
    <div id="suggestionsContainer" style="display: none;">
        <!-- Basic Suggestions -->
        <div id="basicSuggestionsCard" class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Basic Suggestions 
                    <span class="badge bg-light text-dark ms-2" id="basicCount">0</span>
                </h5>
            </div>
            <div class="card-body">
                <div id="basicSuggestionsContent">
                    <!-- Dynamically populated -->
                </div>
            </div>
        </div>
        
        <!-- Premium Suggestions -->
        <div id="premiumSuggestionsCard" class="card mb-4" style="display: none;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-crown me-2"></i>
                    Premium Suggestions 
                    <span class="badge bg-dark text-light ms-2" id="premiumCount">0</span>
                </h5>
            </div>
            <div class="card-body">
                <div id="premiumSuggestionsContent">
                    <!-- Dynamically populated -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Indicator -->
<div id="loadingIndicator" class="text-center py-5" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3">Generating suggestions...</p>
</div>

<!-- Alert Container -->
<div id="alertContainer" class="container">
    <!-- Dynamic alerts -->
</div>
```

---

## 🎨 **CSS Styling System**

### **File**: `us10/frontend/static/css/us10_styles.css`

The CSS follows a **component-based architecture** with consistent design tokens:

```css
/* ===== DESIGN TOKENS ===== */
:root {
    /* Colors */
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Typography */
    --font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 1rem;
    --line-height-base: 1.5;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* Borders */
    --border-radius: 0.375rem;
    --border-width: 1px;
    
    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* ===== GLOBAL STYLES ===== */
body {
    font-family: var(--font-family-base);
    line-height: var(--line-height-base);
    color: #333;
    background-color: #f8f9fa;
}

/* ===== COMPONENT STYLES ===== */

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--shadow-sm);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: transparent;
    border-bottom: var(--border-width) solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: var(--border-width) solid #ced4da;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* ===== PAGE-SPECIFIC STYLES ===== */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
}

@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    color: var(--primary-color);
}

/* Dashboard Stats */
.stat-card {
    border-radius: var(--border-radius);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: scale(1.05);
}

/* Suggestions Page */
.suggestions-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.basic-suggestion-card {
    border-left: 4px solid var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.premium-suggestion-card {
    border-left: 4px solid var(--warning-color);
    margin-bottom: var(--spacing-md);
}

.priority-critical {
    border-left-color: var(--danger-color) !important;
}

.priority-high {
    border-left-color: var(--warning-color) !important;
}

.priority-medium {
    border-left-color: var(--info-color) !important;
}

.priority-low {
    border-left-color: var(--success-color) !important;
}

/* Match Score Display */
.match-score-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin: 0 auto;
}

.score-excellent { background: var(--success-color); }
.score-good { background: var(--info-color); }
.score-fair { background: var(--warning-color); }
.score-poor { background: var(--danger-color); }

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
}

.shadow-hover {
    transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg);
}
```

---

## ⚡ **JavaScript Architecture**

The JavaScript layer provides interactive functionality using a **modular, class-based architecture**:

### **Base Architecture Pattern**
```javascript
class PageManager {
    constructor() {
        this.token = localStorage.getItem('access_token');
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.loadInitialData();
    }

    checkAuthentication() {
        if (!this.token) {
            this.redirectToLogin();
            return false;
        }
        return true;
    }

    async makeAuthenticatedRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, mergedOptions);

            if (response.status === 401) {
                this.handleAuthError();
                return null;
            }

            return response;
        } catch (error) {
            this.handleNetworkError(error);
            return null;
        }
    }

    handleAuthError() {
        localStorage.removeItem('access_token');
        window.location.href = '/login';
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.appendChild(alertDiv);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
```

### **Suggestions Page JavaScript**
**File**: `us10/frontend/static/js/us10_suggestions.js`

```javascript
class SuggestionsManager extends PageManager {
    constructor() {
        super();
        this.currentSuggestions = null;
        this.currentType = null;
    }

    init() {
        super.init();
        this.loadAvailableData();
    }

    setupEventListeners() {
        // Generate buttons
        document.getElementById('generateBasicBtn')?.addEventListener('click', () => {
            this.generateBasicSuggestions();
        });

        document.getElementById('generatePremiumBtn')?.addEventListener('click', () => {
            this.generatePremiumSuggestions();
        });

        // Selection change handlers
        document.getElementById('resumeSelect')?.addEventListener('change', () => {
            this.validateSelections();
        });

        document.getElementById('jobSelect')?.addEventListener('change', () => {
            this.validateSelections();
        });
    }

    async loadAvailableData() {
        try {
            const response = await this.makeAuthenticatedRequest('/api/available_suggestions');

            if (!response) return;

            if (response.ok) {
                const data = await response.json();

                if (data.success) {
                    this.populateSelects(data.resumes, data.job_descriptions);

                    if (data.resumes.length === 0 && data.job_descriptions.length === 0) {
                        this.showAlert('No resumes or job descriptions found. Please upload a resume and add a job description first.', 'info');
                    }
                } else {
                    throw new Error(data.message || 'Failed to load data');
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || 'Failed to load data');
            }
        } catch (error) {
            console.error('Error loading available data:', error);
            this.showAlert('Error loading resumes and job descriptions.', 'danger');
        }
    }

    populateSelects(resumes, jobDescriptions) {
        const resumeSelect = document.getElementById('resumeSelect');
        const jobSelect = document.getElementById('jobSelect');

        if (!resumeSelect || !jobSelect) {
            console.error('Resume or job select elements not found');
            return;
        }

        // Clear existing options
        resumeSelect.innerHTML = '<option value="">Select a resume...</option>';
        jobSelect.innerHTML = '<option value="">Select a job description...</option>';

        // Populate resumes
        resumes.forEach(resume => {
            const option = document.createElement('option');
            option.value = resume.id;
            option.textContent = resume.original_filename;
            resumeSelect.appendChild(option);
        });

        // Populate job descriptions
        jobDescriptions.forEach(jd => {
            const option = document.createElement('option');
            option.value = jd.id;
            option.textContent = `${jd.title} - ${jd.company_name || 'Unknown Company'}`;
            jobSelect.appendChild(option);
        });
    }

    async generateBasicSuggestions() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showAlert('Please select both a resume and job description.', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await this.makeAuthenticatedRequest('/api/basic_suggestions', {
                method: 'POST',
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId)
                })
            });

            if (!response) return;

            const data = await response.json();

            if (response.ok) {
                this.displaySuggestions(data.suggestions, 'basic');
                if (data.matching_score) {
                    this.displayMatchingScore(data.matching_score, 'basic');
                }
                this.showAlert(`Generated ${data.total_suggestions || data.suggestions?.length || 0} basic suggestions!`, 'success');
            } else {
                throw new Error(data.message || 'Failed to generate suggestions');
            }
        } catch (error) {
            console.error('Error generating basic suggestions:', error);
            this.showAlert('Error generating basic suggestions. Please try again.', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    async generatePremiumSuggestions() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showAlert('Please select both a resume and job description.', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await this.makeAuthenticatedRequest('/api/premium_suggestions', {
                method: 'POST',
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId)
                })
            });

            if (!response) return;

            const data = await response.json();

            if (response.ok) {
                this.displaySuggestions(data.suggestions, 'premium');
                if (data.matching_score) {
                    this.displayMatchingScore(data.matching_score, 'premium');
                }
                this.showAlert(`Generated ${data.total_suggestions || data.suggestions?.length || 0} premium suggestions!`, 'success');
            } else {
                throw new Error(data.message || 'Failed to generate premium suggestions');
            }
        } catch (error) {
            console.error('Error generating premium suggestions:', error);
            this.showAlert('Error generating premium suggestions. Please try again.', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displaySuggestions(suggestions, type) {
        const container = document.getElementById('suggestionsContainer');
        const basicCard = document.getElementById('basicSuggestionsCard');
        const premiumCard = document.getElementById('premiumSuggestionsCard');

        if (type === 'basic') {
            // Show ONLY basic suggestions, hide premium
            basicCard.style.display = 'block';
            premiumCard.style.display = 'none';

            // Update basic card count
            const basicCount = document.getElementById('basicCount');
            if (basicCount) {
                basicCount.textContent = suggestions.length;
            }

            // Display basic suggestions
            this.displayBasicSuggestions(suggestions);

        } else if (type === 'premium') {
            // Show ONLY premium suggestions, hide basic
            basicCard.style.display = 'none';
            premiumCard.style.display = 'block';

            // Update premium card count
            const premiumCount = document.getElementById('premiumCount');
            if (premiumCount) {
                premiumCount.textContent = suggestions.length;
            }

            // Display premium suggestions
            this.displayPremiumSuggestions(suggestions);
        }

        // Show the main container
        container.style.display = 'block';

        // Store suggestions
        this.currentSuggestions = suggestions;
        this.currentType = type;
    }

    displayBasicSuggestions(suggestions) {
        const content = document.getElementById('basicSuggestionsContent');

        // Group suggestions by category
        const categories = {};
        suggestions.forEach(suggestion => {
            const category = suggestion.type || 'general';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push(suggestion);
        });

        let html = '';

        // Create sections for each category
        Object.keys(categories).forEach(category => {
            const categoryTitle = this.formatCategoryTitle(category);
            const categoryIcon = this.getCategoryIcon(category);

            html += `
                <div class="suggestion-category mb-4">
                    <h6 class="category-title mb-3">
                        <i class="${categoryIcon} me-2"></i>${categoryTitle}
                    </h6>
                    <div class="row">
            `;

            categories[category].forEach((suggestion, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        ${this.createBasicSuggestionCard(suggestion, index)}
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        content.innerHTML = html;
    }

    createBasicSuggestionCard(suggestion, index) {
        const priority = suggestion.priority || 'medium';
        const priorityClass = this.getPriorityClass(priority);
        const priorityIcon = this.getPriorityIcon(priority);

        return `
            <div class="card basic-suggestion-card priority-${priority}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">${suggestion.title}</h6>
                        <span class="badge ${priorityClass}">
                            <i class="${priorityIcon} me-1"></i>${priority.toUpperCase()}
                        </span>
                    </div>

                    <p class="card-text text-muted small mb-2">
                        ${suggestion.description}
                    </p>

                    <div class="suggestion-action mb-2">
                        <strong>Action:</strong>
                        <p class="mb-0 small">${suggestion.action}</p>
                    </div>

                    ${suggestion.example ? `
                        <div class="suggestion-example">
                            <strong>Example:</strong>
                            <p class="mb-0 small text-success">${suggestion.example}</p>
                        </div>
                    ` : ''}

                    ${suggestion.keywords && suggestion.keywords.length > 0 ? `
                        <div class="suggestion-keywords mt-2">
                            ${suggestion.keywords.map(keyword =>
                                `<span class="badge bg-light text-dark me-1">${keyword}</span>`
                            ).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    displayMatchingScore(matchingScore, type) {
        const scoreContent = document.getElementById('matchingScoreContent');
        if (!scoreContent || !matchingScore) return;

        // Determine overall score color and message
        const overallScore = matchingScore.overall_score || 0;
        let scoreColor, scoreMessage, recommendationText;

        if (overallScore >= 80) {
            scoreColor = 'success';
            scoreMessage = 'Excellent Match!';
            recommendationText = 'Your resume is very well-aligned with the job requirements.';
        } else if (overallScore >= 60) {
            scoreColor = 'warning';
            scoreMessage = 'Good Match';
            recommendationText = 'Your resume covers most requirements but could benefit from improvements.';
        } else if (overallScore >= 40) {
            scoreColor = 'info';
            scoreMessage = 'Moderate Match';
            recommendationText = 'Your resume has a solid foundation but needs enhancement in key areas.';
        } else {
            scoreColor = 'danger';
            scoreMessage = 'Needs Improvement';
            recommendationText = 'Your resume has significant gaps. Focus on adding missing skills.';
        }

        scoreContent.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="d-flex align-items-center mb-2">
                        <h4 class="text-${scoreColor} mb-0 me-3">${overallScore.toFixed(1)}%</h4>
                        <span class="badge bg-${scoreColor} fs-6">${scoreMessage}</span>
                    </div>
                    <p class="text-muted mb-0">${recommendationText}</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Technical Skills</span>
                            <span class="badge bg-${this.getScoreColor(matchingScore.technical_score || 0)}">${(matchingScore.technical_score || 0).toFixed(1)}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${this.getScoreColor(matchingScore.technical_score || 0)}"
                                 style="width: ${matchingScore.technical_score || 0}%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Soft Skills</span>
                            <span class="badge bg-${this.getScoreColor(matchingScore.soft_skills_score || 0)}">${(matchingScore.soft_skills_score || 0).toFixed(1)}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${this.getScoreColor(matchingScore.soft_skills_score || 0)}"
                                 style="width: ${matchingScore.soft_skills_score || 0}%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Other Keywords</span>
                            <span class="badge bg-${this.getScoreColor(matchingScore.other_keywords_score || 0)}">${(matchingScore.other_keywords_score || 0).toFixed(1)}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${this.getScoreColor(matchingScore.other_keywords_score || 0)}"
                                 style="width: ${matchingScore.other_keywords_score || 0}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Utility methods
    formatCategoryTitle(category) {
        return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    getCategoryIcon(category) {
        const icons = {
            'technical_skills': 'fas fa-code',
            'soft_skills': 'fas fa-users',
            'industry_keywords': 'fas fa-industry',
            'ats_optimization': 'fas fa-robot',
            'general': 'fas fa-lightbulb'
        };
        return icons[category] || 'fas fa-lightbulb';
    }

    getPriorityClass(priority) {
        const classes = {
            'critical': 'bg-danger',
            'high': 'bg-warning text-dark',
            'medium': 'bg-info',
            'low': 'bg-success'
        };
        return classes[priority.toLowerCase()] || 'bg-secondary';
    }

    getPriorityIcon(priority) {
        const icons = {
            'critical': 'fas fa-exclamation-triangle',
            'high': 'fas fa-exclamation',
            'medium': 'fas fa-info',
            'low': 'fas fa-check'
        };
        return icons[priority.toLowerCase()] || 'fas fa-info';
    }

    getScoreColor(score) {
        if (score >= 80) return 'success';
        if (score >= 60) return 'info';
        if (score >= 40) return 'warning';
        return 'danger';
    }

    showLoading(show) {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = show ? 'block' : 'none';
        }

        // Disable buttons during loading
        const buttons = ['generateBasicBtn', 'generatePremiumBtn'];
        buttons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.disabled = show;
                if (show) {
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
                } else {
                    // Restore original text
                    if (btnId === 'generateBasicBtn') {
                        btn.innerHTML = '<i class="fas fa-check me-2"></i>Generate Basic Suggestions';
                    } else {
                        btn.innerHTML = '<i class="fas fa-crown me-2"></i>Generate Premium Suggestions';
                    }
                }
            }
        });
    }

    validateSelections() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        const basicBtn = document.getElementById('generateBasicBtn');
        const premiumBtn = document.getElementById('generatePremiumBtn');

        const isValid = resumeId && jobId;

        if (basicBtn) basicBtn.disabled = !isValid;
        if (premiumBtn) premiumBtn.disabled = !isValid;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.suggestionsManager = new SuggestionsManager();
});
```

---

## 🔄 **User Interface Flow**

### **Complete User Journey**
1. **Landing** → User sees features and benefits
2. **Registration** → Creates account with validation
3. **Login** → Authenticates and receives JWT token
4. **Dashboard** → Central hub showing overview
5. **Upload Resume** → Parses PDF/DOCX files
6. **Add Job Description** → Creates job posting
7. **Extract Keywords** → NLP analysis of content
8. **Calculate Match** → Jaccard similarity scoring
9. **Generate Suggestions** → AI-powered recommendations
10. **Account Management** → Profile and settings

### **State Management**
- **Authentication**: JWT tokens in localStorage
- **Form Data**: Temporary state in component classes
- **API Responses**: Cached for performance
- **UI State**: Managed through CSS classes and DOM manipulation

---

## 🔗 **Integration Patterns**

### **API Communication Pattern**
```javascript
// Consistent error handling across all pages
async makeAuthenticatedRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            ...options
        });

        if (response.status === 401) {
            this.handleAuthError();
            return null;
        }

        return response;
    } catch (error) {
        this.handleNetworkError(error);
        return null;
    }
}
```

### **Component Lifecycle Pattern**
```javascript
class ComponentManager {
    constructor() {
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Bind event handlers
    }

    async loadInitialData() {
        // Fetch required data
    }
}
```

---

## 📱 **Responsive Design**

### **Breakpoint Strategy**
- **Mobile First**: Base styles for mobile devices
- **Tablet**: 768px and up
- **Desktop**: 992px and up
- **Large Desktop**: 1200px and up

### **Key Responsive Features**
- **Navigation**: Collapsible mobile menu
- **Cards**: Stack on mobile, grid on desktop
- **Forms**: Full-width on mobile, constrained on desktop
- **Tables**: Horizontal scroll on mobile
- **Buttons**: Full-width on mobile, inline on desktop

---

**🔄 Next**: [Feature Integration Guide](README-Integration.md) - Learn how US1-US10 features connect together
